import 'package:Kairos/core/api/api_endpoints.dart';
import 'package:Kairos/core/error/exceptions.dart';
import 'package:Kairos/features/student_records/data/models/dossier_model.dart';
import 'dart:convert';
import 'package:dio/dio.dart';
import '../../../../core/api/api_client.dart';


abstract class DossierRemoteDataSource {
  /// Fetches the list of dossiers for a student.
  ///
  /// Throws a [ServerException] for all error codes.
  Future<List<DossierModel>> getDossiers({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Fetches the list of dossiers for a student filtered by year.
  ///
  /// Throws a [ServerException] for all error codes.
  Future<List<DossierModel>> getDossiersByYear({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String annee,
    String? codeUtilisateur,
  });
}

class DossierRemoteDataSourceImpl implements DossierRemoteDataSource {
  final ApiClient apiClient;

  DossierRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<DossierModel>> getDossiers({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {

       Map<String, dynamic> queryParams = {
          'codeEtab': codeEtab,
          'telephone': telephone.replaceAll("+",""),
          'codeEtudiant': codeEtudiant,
        };

      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParams['codeUtilisateur'] = codeUtilisateur;
      }


      final response = await apiClient.getWithToken(
        ApiEndpoints.documentsEtudiant,
       queryParameters: queryParams,
       options: Options(responseType: ResponseType.bytes)
      );

      final String decodedResponse = latin1.decode(response.data);
      final jsonResponse = jsonDecode(decodedResponse);


      if (jsonResponse is List) {
        final List<dynamic> jsonList = jsonResponse;
        return jsonList.map((json) => DossierModel.fromJson(json)).toList();
      } else {
        throw Exception('Invalid response format: expected List but got ${response.data.runtimeType}');
      }

    } catch (e) {
      throw ServerException('Failed to load dossiers: $e');
    }
  }

  @override
  Future<List<DossierModel>> getDossiersByYear({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String annee,
    String? codeUtilisateur,
  }) async {
    try {
      Map<String, dynamic> queryParams = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
        'annee': annee,
      };

      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParams['codeUtilisateur'] = codeUtilisateur;
      }

      final response = await apiClient.getWithToken(
        ApiEndpoints.documentsEtudiantAnnee,
        queryParameters: queryParams,
        options: Options(responseType: ResponseType.bytes),
      );

      final String decodedResponse = latin1.decode(response.data);
      final jsonResponse = jsonDecode(decodedResponse);

      if (jsonResponse is List) {
        final List<dynamic> jsonList = jsonResponse;
        return jsonList.map((json) => DossierModel.fromJson(json)).toList();
      } else {
        throw Exception('Invalid response format: expected List but got ${response.data.runtimeType}');
      }
    } catch (e) {
      throw ServerException('Failed to load dossiers by year: $e');
    }
  }
}