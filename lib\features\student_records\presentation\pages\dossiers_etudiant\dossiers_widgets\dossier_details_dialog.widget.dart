


import 'package:Kairos/features/student_records/domain/entities/dossier_entity.dart'; // Import DossierEntity
import 'package:Kairos/features/student_records/presentation/bloc/dossiers_cubit.dart';
import 'package:Kairos/features/student_records/presentation/bloc/dossiers_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart'; // Import flutter_html
import 'package:shared_preferences/shared_preferences.dart';

class DossierDetailsDialogWidget extends StatelessWidget {
  const DossierDetailsDialogWidget({super.key, required this.dossier});
  final DossierEntity dossier; // Change type to DossierEntity


  @override
  Widget build(BuildContext context){
    return BlocListener<DossiersCubit, DossiersState>(
      listener: (context, state) {
        if (state is AttachmentError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        } else if (state is AttachmentLoaded) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Fichier ouvert avec succès'),
              backgroundColor: Colors.green,
            ),
          );
        }
      },
      child: Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 10),
      backgroundColor: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: [
                  ListTile(
                          leading: CircleAvatar(radius: 30, 
                                                // Use color from API response, fallback to primary color
                                                backgroundColor: Color(int.parse(dossier.couleurTypeSuivi.replaceAll('#', '0xFF'))),
                                                child: Text(getInitials(dossier.typeSuivi), style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),)), // Use typeSuivi for initials
                        title: Text(dossier.typeSuivi, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),), // Use typeSuivi
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(dossier.objetSuivi), // Use objetSuivi
                            // Text(dossier.description, overflow: TextOverflow.ellipsis, maxLines: 1, style: TextStyle(color: Colors.grey)), // Remove old description
                          ],
                        ),
                  ),
                     Positioned(
                    top: 0,
                    right: 0, 
                    child: 
                    Container(
                      decoration: BoxDecoration(color: Theme.of(context).colorScheme.secondary, borderRadius: BorderRadius.circular(100)),
                      child:InkWell(
                    borderRadius: BorderRadius.circular(14),
                    onTap: () => Navigator.of(context).pop(),
                    child: const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Icon(Icons.close, color: Colors.white, size: 20),
                    ),
                  ),
                                        )
                ),
              ],),
                // SizedBox(height: 20),
                Divider(color: Theme.of(context).primaryColor, thickness: 1, ),
                Text.rich(
                  TextSpan(children: [
                    TextSpan(text: "Objet: ", style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(text: dossier.objetSuivi), // Use objetSuivi
                  ])
                ),
                SizedBox(height: 3),

                // Use HtmlWidget to render contenuSuivi
                Flexible(
                  child: SingleChildScrollView( // Wrap with SingleChildScrollView for potentially long content
                    child: Html(
                      data: dossier.contenuSuivi, // Use contenuSuivi
                    ),
                  ),
                ),
                SizedBox(height: 20),
                // Only show the attachment button if the dossier has an attachment
                if (dossier.indDocumentSuivi)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      BlocBuilder<DossiersCubit, DossiersState>(
                        builder: (context, state) {
                          final isLoading = state is AttachmentLoading;

                          return FilledButton(
                            onPressed: isLoading ? null : () => _openAttachment(context),
                            style: ButtonStyle(
                              minimumSize: WidgetStateProperty.all(Size(150, 50)),
                              backgroundColor: WidgetStateProperty.all(
                                isLoading ? Colors.grey : Theme.of(context).primaryColor
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (isLoading)
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    ),
                                  )
                                else
                                  Icon(Icons.download),
                                SizedBox(width: 8),
                                Text(isLoading ? "Chargement..." : "Ouvrir la pièce jointe"),
                              ],
                            ),
                          );
                        },
                      ),
                    ],
                  )

          ],
        ),
      ),
    ),
    );
  }

  // Method to handle attachment opening
  Future<void> _openAttachment(BuildContext context) async {
    try {
      // Get SharedPreferences to retrieve user data
      final prefs = await SharedPreferences.getInstance();
      final codeEtab = prefs.getString('codeEtab') ?? '';
      final telephone = prefs.getString('telephone') ?? '';
      final codeEtudiant = prefs.getString('codeEtudiant') ?? '';
      final codeUtilisateur = prefs.getString('codeUtilisateur');

      // Check if context is still mounted after async operation
      if (!context.mounted) return;

      // Check if we have the required parameters
      if (codeEtab.isEmpty || telephone.isEmpty || codeEtudiant.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Données utilisateur manquantes. Veuillez vous reconnecter.'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Call the cubit to fetch and open the attachment
      context.read<DossiersCubit>().fetchDossierAttachment(
        codeEtab: codeEtab,
        numeroTel: telephone,
        codeEtudiant: codeEtudiant,
        idObject: dossier.idObjet,
        codeUtilisateur: codeUtilisateur,
        fileName: 'dossier_${dossier.idObjet}_${dossier.typeSuivi.replaceAll(' ', '_')}',
      );
    } catch (e) {
      // Check if context is still mounted before showing snackbar
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'ouverture de la pièce jointe: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Helper method to get initials from a string
  String getInitials(String text) {
    if (text.isEmpty) return '';
    List<String> words = text.split(' ');
    if (words.length >= 2) {
      return words[0][0].toUpperCase() + words[1][0].toUpperCase();
    } else {
      return text[0].toUpperCase();
    }
  }
}