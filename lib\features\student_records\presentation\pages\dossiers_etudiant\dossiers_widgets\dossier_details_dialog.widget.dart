



import 'package:Kairos/features/student_records/domain/entities/dossier_entity.dart'; // Import DossierEntity
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart'; // Import flutter_html

class DossierDetailsDialogWidget extends StatelessWidget {
  const DossierDetailsDialogWidget({super.key, required this.dossier});
  final DossierEntity dossier; // Change type to DossierEntity


  @override
  Widget build(BuildContext context){
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 10),
      backgroundColor: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: [
                  ListTile(
                          leading: CircleAvatar(radius: 30, 
                                                // Use color from API response, fallback to primary color
                                                backgroundColor: Color(int.parse(dossier.couleurTypeSuivi.replaceAll('#', '0xFF'))),
                                                child: Text(getInitials(dossier.typeSuivi), style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),)), // Use typeSuivi for initials
                        title: Text(dossier.typeSuivi, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),), // Use typeSuivi
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(dossier.objetSuivi), // Use objetSuivi
                            // Text(dossier.description, overflow: TextOverflow.ellipsis, maxLines: 1, style: TextStyle(color: Colors.grey)), // Remove old description
                          ],
                        ),
                  ),
                     Positioned(
                    top: 0,
                    right: 0, 
                    child: 
                    Container(
                      decoration: BoxDecoration(color: Theme.of(context).colorScheme.secondary, borderRadius: BorderRadius.circular(100)),
                      child:InkWell(
                    borderRadius: BorderRadius.circular(14),
                    onTap: () => Navigator.of(context).pop(),
                    child: const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Icon(Icons.close, color: Colors.white, size: 20),
                    ),
                  ),
                                        )
                ),
              ],),
                // SizedBox(height: 20),
                Divider(color: Theme.of(context).primaryColor, thickness: 1, ),
                Text.rich(
                  TextSpan(children: [
                    TextSpan(text: "Objet: ", style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(text: dossier.objetSuivi), // Use objetSuivi
                  ])
                ),
                SizedBox(height: 3),

                // Use HtmlWidget to render contenuSuivi
                Flexible(
                  child: SingleChildScrollView( // Wrap with SingleChildScrollView for potentially long content
                    child: Html(
                      data: dossier.contenuSuivi, // Use contenuSuivi
                    ),
                  ),
                ),
                SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                     FilledButton(
                      onPressed: (){

                        
                      },
                      style: ButtonStyle(
                        minimumSize: WidgetStateProperty.all(Size(150, 50)),
                        backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.download),
                          Text("Ouvrir la piéce jointe"),
                        ],
                      ),
                    ),
                  ],
                )

          ],
        ),
      ),
    );
  }

  // Helper method to get initials from a string
  String getInitials(String text) {
    if (text.isEmpty) return '';
    List<String> words = text.split(' ');
    if (words.length >= 2) {
      return words[0][0].toUpperCase() + words[1][0].toUpperCase();
    } else {
      return text[0].toUpperCase();
    }
  }
}