import 'package:Kairos/core/error/failures.dart';
import 'package:Kairos/features/student_records/domain/usecases/get_dossiers.dart';
import 'package:Kairos/features/student_records/domain/usecases/get_dossiers_by_year.dart'; // Import the new use case
import "package:flutter_bloc/flutter_bloc.dart";
import 'dossiers_state.dart';


class DossiersCubit extends Cubit<DossiersState> {
  final GetDossiersUseCase getDossiersUseCase;
  final GetDossiersByYearUseCase getDossiersByYearUseCase; // Add the new use case

  DossiersCubit({
    required this.getDossiersUseCase,
    required this.getDossiersByYearUseCase, // Initialize the new use case
  }) : super(DossiersInitial());

  Future<void> fetchDossiers({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    emit(DossiersLoading());
    final failureOrDossiers = await getDossiersUseCase(
      GetDossiersParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      ),
    );

    failureOrDossiers.fold(
      (failure) {
        if (failure is ServerFailure) {
          emit(DossiersError('Failed to fetch dossiers: Server Error'));
        } else if (failure is NetworkFailure) {
          emit(DossiersError('Failed to fetch dossiers: Network Error'));
        } else {
          emit(DossiersError('Failed to fetch dossiers: Unknown Error'));
        }
      },
      (dossiers) => emit(DossiersLoaded(dossiers)),
    );
  }

  /// Fetches student dossiers filtered by year.
  /// This method handles the state management for fetching dossiers based on the academic year,
  /// emitting loading, error, or loaded states as appropriate.
  Future<void> fetchDossiersByYear({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String annee,
    String? codeUtilisateur,
  }) async {
    emit(DossiersLoading());
    final failureOrDossiers = await getDossiersByYearUseCase(
      GetDossiersByYearParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        annee: annee,
        codeUtilisateur: codeUtilisateur,
      ),
    );

    failureOrDossiers.fold(
      (failure) {
        if (failure is ServerFailure) {
          emit(DossiersError('Failed to fetch dossiers by year: Server Error'));
        } else if (failure is NetworkFailure) {
          emit(DossiersError('Failed to fetch dossiers by year: Network Error'));
        } else {
          emit(DossiersError('Failed to fetch dossiers by year: Unknown Error'));
        }
      },
      (dossiers) => emit(DossiersLoaded(dossiers)),
    );
  }
}