import 'dart:convert';
import 'dart:io';
import 'package:Kairos/core/error/failures.dart';
import 'package:Kairos/features/student_records/domain/usecases/get_dossiers.dart';
import 'package:Kairos/features/student_records/domain/usecases/get_dossiers_by_year.dart'; // Import the new use case
import 'package:Kairos/features/student_records/domain/usecases/get_dossier_attachment.dart';
import "package:flutter_bloc/flutter_bloc.dart";
import 'package:path_provider/path_provider.dart';
import 'package:open_file/open_file.dart';
import 'dossiers_state.dart';


class DossiersCubit extends Cubit<DossiersState> {
  final GetDossiersUseCase getDossiersUseCase;
  final GetDossiersByYearUseCase getDossiersByYearUseCase; // Add the new use case
  final GetDossierAttachmentUseCase getDossierAttachmentUseCase;

  DossiersCubit({
    required this.getDossiersUseCase,
    required this.getDossiersByYearUseCase, // Initialize the new use case
    required this.getDossierAttachmentUseCase,
  }) : super(DossiersInitial());

  Future<void> fetchDossiers({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    emit(DossiersLoading());
    final failureOrDossiers = await getDossiersUseCase(
      GetDossiersParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      ),
    );

    failureOrDossiers.fold(
      (failure) {
        if (failure is ServerFailure) {
          emit(DossiersError('Failed to fetch dossiers: Server Error'));
        } else if (failure is NetworkFailure) {
          emit(DossiersError('Failed to fetch dossiers: Network Error'));
        } else {
          emit(DossiersError('Failed to fetch dossiers: Unknown Error'));
        }
      },
      (dossiers) => emit(DossiersLoaded(dossiers)),
    );
  }

  /// Fetches student dossiers filtered by year.
  /// This method handles the state management for fetching dossiers based on the academic year,
  /// emitting loading, error, or loaded states as appropriate.
  Future<void> fetchDossiersByYear({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String annee,
    String? codeUtilisateur,
  }) async {
    emit(DossiersLoading());
    final failureOrDossiers = await getDossiersByYearUseCase(
      GetDossiersByYearParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        annee: annee,
        codeUtilisateur: codeUtilisateur,
      ),
    );

    failureOrDossiers.fold(
      (failure) {
        if (failure is ServerFailure) {
          emit(DossiersError('Failed to fetch dossiers by year: Server Error'));
        } else if (failure is NetworkFailure) {
          emit(DossiersError('Failed to fetch dossiers by year: Network Error'));
        } else {
          emit(DossiersError('Failed to fetch dossiers by year: Unknown Error'));
        }
      },
      (dossiers) => emit(DossiersLoaded(dossiers)),
    );
  }

  /// Fetches and opens a dossier attachment file.
  /// This method handles the complete flow from fetching the base64 content
  /// to saving it locally and opening it with the system default application.
  Future<void> fetchDossierAttachment({
    required String codeEtab,
    required String numeroTel,
    required String codeEtudiant,
    required int idObject,
    String? codeUtilisateur,
    String? fileName,
  }) async {
    emit(AttachmentLoading());

    final failureOrAttachment = await getDossierAttachmentUseCase(
      GetDossierAttachmentParams(
        codeEtab: codeEtab,
        numeroTel: numeroTel,
        codeEtudiant: codeEtudiant,
        idObject: idObject,
        codeUtilisateur: codeUtilisateur,
      ),
    );

    failureOrAttachment.fold(
      (failure) {
        if (failure is ServerFailure) {
          emit(AttachmentError('Failed to fetch attachment: Server Error'));
        } else if (failure is NetworkFailure) {
          emit(AttachmentError('Failed to fetch attachment: Network Error'));
        } else {
          emit(AttachmentError('Failed to fetch attachment: Unknown Error'));
        }
      },
      (attachment) async {
        try {
          // Check if the API returned success
          if (attachment.returnCode != 'SUCCESS') {
            emit(AttachmentError('Failed to fetch attachment: ${attachment.returnCode}'));
            return;
          }

          // Decode base64 content
          final bytes = base64Decode(attachment.file);

          // Get app documents directory
          final directory = await getApplicationDocumentsDirectory();

          // Generate filename based on type and current timestamp
          final extension = _getFileExtension(attachment.type);
          final timestamp = DateTime.now().millisecondsSinceEpoch;
          final finalFileName = fileName ?? 'dossier_attachment_$timestamp$extension';

          // Create file path
          final filePath = '${directory.path}/$finalFileName';

          // Write file to storage
          final file = File(filePath);
          await file.writeAsBytes(bytes);

          // Emit success state
          emit(AttachmentLoaded(attachment, filePath));

          // Open file with system default application
          final result = await OpenFile.open(filePath);
          if (result.type != ResultType.done) {
            emit(AttachmentError('Failed to open file: ${result.message}'));
          }
        } catch (e) {
          emit(AttachmentError('Failed to process attachment: $e'));
        }
      },
    );
  }

  /// Helper method to get file extension from MIME type
  String _getFileExtension(String mimeType) {
    switch (mimeType.toLowerCase()) {
      case 'application/pdf':
        return '.pdf';
      case 'image/jpeg':
      case 'image/jpg':
        return '.jpg';
      case 'image/png':
        return '.png';
      case 'image/gif':
        return '.gif';
      case 'application/msword':
        return '.doc';
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return '.docx';
      case 'application/vnd.ms-excel':
        return '.xls';
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return '.xlsx';
      case 'text/plain':
        return '.txt';
      default:
        return '.bin'; // Generic binary file extension
    }
  }
}