import 'package:dartz/dartz.dart';
import 'package:Kairos/core/error/failures.dart';
import 'package:Kairos/features/student_records/domain/entities/dossier_entity.dart';

abstract class DossierRepository {
  /// Retrieves a list of dossiers for a specific student.
  ///
  /// Returns a [Future] that resolves to an [Either] containing a [Failure]
  /// on the left side if an error occurs, or a [List] of [DossierEntity]
  /// on the right side if the operation is successful.
  Future<Either<Failure, List<DossierEntity>>> getDossiers({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Retrieves a list of dossiers for a specific student filtered by year.
  ///
  /// Returns a [Future] that resolves to an [Either] containing a [Failure]
  /// on the left side if an error occurs, or a [List] of [DossierEntity]
  /// on the right side if the operation is successful.
  Future<Either<Failure, List<DossierEntity>>> getDossiersByYear({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String annee,
    String? codeUtilisateur,
  });
}